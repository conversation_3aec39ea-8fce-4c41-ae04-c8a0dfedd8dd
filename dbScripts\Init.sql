-- ==========================================
-- CREATE TABLES
-- ==========================================

-- Independent tables (no foreign keys)

CREATE TABLE IF NOT EXISTS public."User" (
    "Id" UUID NOT NULL PRIMARY KEY,
    "FirstName" VARCHAR(100) NOT NULL,
    "LastName" VARCHAR(100) NULL,
    "Email" VARCHAR(100) NOT NULL,
    "PasswordHash" VARCHAR NOT NULL,
    "LoginFailedAttempts" INT NOT NULL DEFAULT 0,
    "LoginLockoutDate" TIMESTAMP NULL,
    "ChangePasswordToken" VARCHAR(200) NULL,
    "ChangePasswordTokenDate" TIMESTAMP NULL,
    "Phone" VARCHAR(20) NULL,
    "IsAdmin" BOOLEAN NOT NULL DEFAULT FALSE,
    "IsWholesale" BOOLEAN NOT NULL DEFAULT FALSE,
    "CompanyName" VARCHAR(200) NULL,
    "Qualification" VARCHAR(200) NULL,
    "IsEmailSubscribed" BOOLEAN NOT NULL DEFAULT TRUE,
    "IsDiscountUser" BOOLEAN NOT NULL DEFAULT FALSE,
    "StripeCustomerId" VARCHAR(100) NULL,
    "IsVerified" BOOLEAN NOT NULL DEFAULT FALSE,
    "VerificationToken" VARCHAR(200) NULL,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT NOW(),
    "ModifiedAt" TIMESTAMP NULL,
    "Deleted" BOOLEAN NOT NULL DEFAULT FALSE
);

CREATE TABLE IF NOT EXISTS public."GlobalSettings" (
    "Code" VARCHAR(50) NOT NULL PRIMARY KEY,
    "Description" VARCHAR(200) NOT NULL,
    "Value" VARCHAR(200) NOT NULL
);

CREATE TABLE IF NOT EXISTS public."Contact" (
    "Id" UUID NOT NULL PRIMARY KEY,
    "UserId" UUID NULL,
    "Name" VARCHAR(200) NOT NULL,
    "Email" VARCHAR(100) NOT NULL,
    "Phone" VARCHAR(20) NULL,
    "Message" VARCHAR NOT NULL,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Option tables

CREATE TABLE IF NOT EXISTS public."O_BuildingType" (
    "Code" VARCHAR(50) NOT NULL PRIMARY KEY,
    "Name" VARCHAR(100) NOT NULL,
    "SortOrder" INT NOT NULL DEFAULT 999,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT NOW(),
    "ModifiedAt" TIMESTAMP NULL,
    "Deleted" BOOLEAN NOT NULL DEFAULT FALSE
);

CREATE TABLE IF NOT EXISTS public."O_CompanyStrataTitle" (
    "Code" VARCHAR(50) NOT NULL PRIMARY KEY,
    "Name" VARCHAR(100) NOT NULL,
    "SortOrder" INT NOT NULL DEFAULT 999,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT NOW(),
    "ModifiedAt" TIMESTAMP NULL,
    "Deleted" BOOLEAN NOT NULL DEFAULT FALSE
);

CREATE TABLE IF NOT EXISTS public."O_NumBedrooms" (
    "Code" VARCHAR(50) NOT NULL PRIMARY KEY,
    "Name" VARCHAR(100) NOT NULL,
    "SortOrder" INT NOT NULL DEFAULT 999,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT NOW(),
    "ModifiedAt" TIMESTAMP NULL,
    "Deleted" BOOLEAN NOT NULL DEFAULT FALSE
);

CREATE TABLE IF NOT EXISTS public."O_Orientation" (
    "Code" VARCHAR(50) NOT NULL PRIMARY KEY,
    "Name" VARCHAR(100) NOT NULL,
    "SortOrder" INT NOT NULL DEFAULT 999,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT NOW(),
    "ModifiedAt" TIMESTAMP NULL,
    "Deleted" BOOLEAN NOT NULL DEFAULT FALSE
);

CREATE TABLE IF NOT EXISTS public."O_Storeys" (
    "Code" VARCHAR(50) NOT NULL PRIMARY KEY,
    "Name" VARCHAR(100) NOT NULL,
    "SortOrder" INT NOT NULL DEFAULT 999,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT NOW(),
    "ModifiedAt" TIMESTAMP NULL,
    "Deleted" BOOLEAN NOT NULL DEFAULT FALSE
);

CREATE TABLE IF NOT EXISTS public."O_Furnished" (
    "Code" VARCHAR(50) NOT NULL PRIMARY KEY,
    "Name" VARCHAR(100) NOT NULL,
    "SortOrder" INT NOT NULL DEFAULT 999,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT NOW(),
    "ModifiedAt" TIMESTAMP NULL,
    "Deleted" BOOLEAN NOT NULL DEFAULT FALSE
);

CREATE TABLE IF NOT EXISTS public."O_Occupied" (
    "Code" VARCHAR(50) NOT NULL PRIMARY KEY,
    "Name" VARCHAR(100) NOT NULL,
    "SortOrder" INT NOT NULL DEFAULT 999,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT NOW(),
    "ModifiedAt" TIMESTAMP NULL,
    "Deleted" BOOLEAN NOT NULL DEFAULT FALSE
);

CREATE TABLE IF NOT EXISTS public."O_Floor" (
    "Code" VARCHAR(50) NOT NULL PRIMARY KEY,
    "Name" VARCHAR(100) NOT NULL,
    "SortOrder" INT NOT NULL DEFAULT 999,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT NOW(),
    "ModifiedAt" TIMESTAMP NULL,
    "Deleted" BOOLEAN NOT NULL DEFAULT FALSE
);

CREATE TABLE IF NOT EXISTS public."O_OtherBuildingElements" (
    "Code" VARCHAR(50) NOT NULL PRIMARY KEY,
    "Name" VARCHAR(100) NOT NULL,
    "SortOrder" INT NOT NULL DEFAULT 999,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT NOW(),
    "ModifiedAt" TIMESTAMP NULL,
    "Deleted" BOOLEAN NOT NULL DEFAULT FALSE
);

CREATE TABLE IF NOT EXISTS public."O_OtherTimberBldgElements" (
    "Code" VARCHAR(50) NOT NULL PRIMARY KEY,
    "Name" VARCHAR(100) NOT NULL,
    "SortOrder" INT NOT NULL DEFAULT 999,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT NOW(),
    "ModifiedAt" TIMESTAMP NULL,
    "Deleted" BOOLEAN NOT NULL DEFAULT FALSE
);

CREATE TABLE IF NOT EXISTS public."O_Roof" (
    "Code" VARCHAR(50) NOT NULL PRIMARY KEY,
    "Name" VARCHAR(100) NOT NULL,
    "SortOrder" INT NOT NULL DEFAULT 999,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT NOW(),
    "ModifiedAt" TIMESTAMP NULL,
    "Deleted" BOOLEAN NOT NULL DEFAULT FALSE
);

CREATE TABLE IF NOT EXISTS public."O_Walls" (
    "Code" VARCHAR(50) NOT NULL PRIMARY KEY,
    "Name" VARCHAR(100) NOT NULL,
    "SortOrder" INT NOT NULL DEFAULT 999,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT NOW(),
    "ModifiedAt" TIMESTAMP NULL,
    "Deleted" BOOLEAN NOT NULL DEFAULT FALSE
);

CREATE TABLE IF NOT EXISTS public."O_Weather" (
    "Code" VARCHAR(50) NOT NULL PRIMARY KEY,
    "Name" VARCHAR(100) NOT NULL,
    "SortOrder" INT NOT NULL DEFAULT 999,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT NOW(),
    "ModifiedAt" TIMESTAMP NULL,
    "Deleted" BOOLEAN NOT NULL DEFAULT FALSE
);

CREATE TABLE IF NOT EXISTS public."O_Location" (
    "Code" VARCHAR(50) NOT NULL PRIMARY KEY,
    "Name" VARCHAR(100) NOT NULL,
    "SortOrder" INT NOT NULL DEFAULT 999,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT NOW(),
    "ModifiedAt" TIMESTAMP NULL,
    "Deleted" BOOLEAN NOT NULL DEFAULT FALSE
);

-- Dependent tables (with foreign keys)

CREATE TABLE IF NOT EXISTS public."UserPersistedData" (
    "Id" UUID NOT NULL PRIMARY KEY,
    "UserId" UUID NOT NULL,
    "Code" VARCHAR(50) NOT NULL,
    "Value" VARCHAR(200) NOT NULL,
    FOREIGN KEY ("UserId") REFERENCES public."User"("Id")
);

CREATE TABLE IF NOT EXISTS public."Property" (
    "Id" UUID NOT NULL PRIMARY KEY,
    "Address" VARCHAR(200) NOT NULL,
    "BuildingTypeCode" VARCHAR(50) NULL,
    "CompanyStrataTitleCode" VARCHAR(50) NULL,
    "NumBedroomsCode" VARCHAR(50) NULL,
    "OrientationCode" VARCHAR(50) NULL,
    "StoreysCode" VARCHAR(50) NULL,
    "FurnishedCode" VARCHAR(50) NULL,
    "OccupiedCode" VARCHAR(50) NULL,
    "FloorCode" VARCHAR(50) NULL,
    "OtherBuildingElementsCode" VARCHAR(50) NULL,
    "OtherTimberBldgElementsCode" VARCHAR(50) NULL,
    "RoofCode" VARCHAR(50) NULL,
    "WallsCode" VARCHAR(50) NULL,
    "WeatherCode" VARCHAR(50) NULL,
    "RoomsListJson" VARCHAR NULL,
    "SortOrder" INT NOT NULL DEFAULT 999,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT NOW(),
    "ModifiedAt" TIMESTAMP NULL,
    "Deleted" BOOLEAN NOT NULL DEFAULT FALSE,
    FOREIGN KEY ("BuildingTypeCode") REFERENCES public."O_BuildingType"("Code"),
    FOREIGN KEY ("CompanyStrataTitleCode") REFERENCES public."O_CompanyStrataTitle"("Code"),
    FOREIGN KEY ("NumBedroomsCode") REFERENCES public."O_NumBedrooms"("Code"),
    FOREIGN KEY ("OrientationCode") REFERENCES public."O_Orientation"("Code"),
    FOREIGN KEY ("StoreysCode") REFERENCES public."O_Storeys"("Code"),
    FOREIGN KEY ("FurnishedCode") REFERENCES public."O_Furnished"("Code"),
    FOREIGN KEY ("OccupiedCode") REFERENCES public."O_Occupied"("Code"),
    FOREIGN KEY ("FloorCode") REFERENCES public."O_Floor"("Code"),
    FOREIGN KEY ("OtherBuildingElementsCode") REFERENCES public."O_OtherBuildingElements"("Code"),
    FOREIGN KEY ("OtherTimberBldgElementsCode") REFERENCES public."O_OtherTimberBldgElements"("Code"),
    FOREIGN KEY ("RoofCode") REFERENCES public."O_Roof"("Code"),
    FOREIGN KEY ("WallsCode") REFERENCES public."O_Walls"("Code"),
    FOREIGN KEY ("WeatherCode") REFERENCES public."O_Weather"("Code")
);

CREATE TABLE IF NOT EXISTS public."PropertyDefect" (
    "Id" UUID NOT NULL PRIMARY KEY,
    "PropertyId" UUID NOT NULL,
    "DefectCategoryCode" VARCHAR(50) NOT NULL,
    "Description" VARCHAR(200) NOT NULL,
    "AreaCode" VARCHAR(50) NOT NULL,
    "LocationCode" VARCHAR(50) NOT NULL,
    "OrientationCode" VARCHAR(50) NOT NULL,
    "Defects" VARCHAR(500) NOT NULL,
    "ServerityCode" VARCHAR(50) NOT NULL,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT NOW(),
    "ModifiedAt" TIMESTAMP NULL,
    "Deleted" BOOLEAN NOT NULL DEFAULT FALSE,
    FOREIGN KEY ("PropertyId") REFERENCES public."Property"("Id")
);

CREATE TABLE IF NOT EXISTS public."DefectImage" (
    "Id" UUID NOT NULL PRIMARY KEY,
    "DefectId" UUID NOT NULL,
    "ImageName" VARCHAR(200) NOT NULL,
    "ImageFileId" UUID NOT NULL,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT NOW(),
    "ModifiedAt" TIMESTAMP NULL,
    "Deleted" BOOLEAN NOT NULL DEFAULT FALSE,
    FOREIGN KEY ("DefectId") REFERENCES public."PropertyDefect"("Id")
);

CREATE TABLE IF NOT EXISTS public."Report" (
    "Id" UUID NOT NULL PRIMARY KEY,
    "PropertyId" UUID NOT NULL,
    "UserId" UUID NOT NULL,
    "Name" VARCHAR(200) NOT NULL,
    "PostalAddress" VARCHAR(200) NOT NULL,
    "ClientName" VARCHAR(200) NOT NULL,
    "ClientPhone" VARCHAR(20) NOT NULL,
    "ClientEmail" VARCHAR(100) NOT NULL,
    "ClientPrincipalName" VARCHAR(200) NOT NULL,
    "InspectionDate" TIMESTAMP NOT NULL,
    "InspectorNameOverride" VARCHAR(200) NULL,
    "InspectorPhoneOverride" VARCHAR(20) NULL,
    "InspectorQualificationOverride" VARCHAR(100) NULL,
    "FileId" UUID NOT NULL,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT NOW(),
    "ModifiedAt" TIMESTAMP NULL,
    "Deleted" BOOLEAN NOT NULL DEFAULT FALSE,
    FOREIGN KEY ("PropertyId") REFERENCES public."Property"("Id"),
    FOREIGN KEY ("UserId") REFERENCES public."User"("Id")
);

-- Add foreign key for Contact table
ALTER TABLE public."Contact" ADD CONSTRAINT "FK_Contact_User"
    FOREIGN KEY ("UserId") REFERENCES public."User"("Id");

-- ==========================================
-- INSERT DATA
-- ==========================================

-- Building Type

INSERT INTO public."O_BuildingType"
("Code",            "Name",                                    "SortOrder")
VALUES
('Duplex',          'Duplex',                                  1),
('HeavyIndustrial', 'Heavy Industrial',                        2),
('Lifestyle',       'Lifestyle or Hobby Farm - Small Acreage', 3),
('LightIndustrial', 'Light Industrial',                        4),
('MultiUnit',       'Multi Unit Property',                     5),
('Residential',     'Residential',                             6),
('ResidentialPool', 'Residential Pool - Shared or Common',     7),
('Rural',           'Rural',                                   8),
('SemiDetached',    'Semi-Detached',                           9),
('Terraced',        'Terraced',                                10),
('Townhouse',       'Townhouse',                               11),
('Unit',            'Unit',                                    12),
('Villa',           'Villa',                                   13),
('Other',           'Other',                                   14);


-- Company or Strata Title

INSERT INTO public."O_CompanyStrataTitle"
("Code",    "Name",    "SortOrder")
VALUES
('No',      'No',      1),
('Yes',     'Yes',     2),
('Unknown', 'Unknown', 3);


-- No. of bedrooms

INSERT INTO public."O_NumBedrooms"
("Code",          "Name",           "SortOrder")
VALUES
('1',             '1',              1),
('2',             '2',              2),
('3',             '3',              3),
('4',             '4',              4),
('5',             '5',              5),
('6',             '6',              6),
('7',             '7',              7),
('8',             '8',              8),
('9',             '9',              9),
('10',            '10',             10),
('NotApplicable', 'Not Applicable', 11);


-- Orientation

INSERT INTO public."O_Orientation"
("Code",      "Name",       "SortOrder")
VALUES
('North',     'North',      1),
('NorthEast', 'North East', 2),
('East',      'East',       3),
('SouthEast', 'South East', 4),
('South',     'South',      5),
('SouthWest', 'South West', 6),
('West',      'West',       7),
('NorthWest', 'North West', 8);


-- Storeys

INSERT INTO public."O_Storeys"
("Code",                "Name",                      "SortOrder")
VALUES
('Double',              'Double',                    1),
('FiveStorey',          'Five Storey',               2),
('FourStorey',          'Four Storey',               3),
('HighSet',             'High-Set',                  4),
('Level',               'Level',                     5),
('MultiStoreyBasement', 'Multi-Storey with basement', 6),
('Single',              'Single',                    7),
('Split',               'Split',                     8),
('ThreeStorey',         'Three Storey',              9),
('Other',               'Other',                     10);


-- Furnished

INSERT INTO public."O_Furnished"
("Code",        "Name",       "SortOrder")
VALUES
('Furnished',   'Furnished',   1),
('Unfurnished', 'Unfurnished', 2);


-- Occupied

INSERT INTO public."O_Occupied"
("Code",       "Name",      "SortOrder")
VALUES
('Occupied',   'Occupied',   1),
('Unoccupied', 'Unoccupied', 2);


-- Floor

INSERT INTO public."O_Floor"
("Code",                         "Name",                                    "SortOrder")
VALUES
('BrickStumpsOrPiers',           'Brick Stumps or Piers',                   1),
('Chipboard',                    'Chipboard',                               2),
('Concrete',                     'Concrete',                                3),
('ConcreteStumps',               'Concrete Stumps',                         4),
('MasonryFoundations',           'Masonry Foundations',                     5),
('MasonryPiers',                 'Masonry Piers',                           6),
('PartSlabAndPartSubfloor',      'Part Slab and Part Subfloor',             7),
('PiersConcrete',                'Piers - Concrete',                        8),
('PiersSteel',                   'Piers - Steel',                           9),
('PiersStone',                   'Piers - Stone',                           10),
('PiersTimber',                  'Piers - Timber',                          11),
('SlabInfillSlab',               'Slab - Infill Slab',                      12),
('SlabMonolithicOrSlabOnGround', 'Slab - Monolithic or Slab on Ground',     13),
('SlabPierAndBeam',              'Slab - Pier and Beam',                    14),
('SlabStiffenedRaftSlab',        'Slab - Stiffened raft slab',              15),
('SlabSuspendedSlab',            'Slab - Suspended Slab',                   16),
('SlabWafflePodOrWaffleSlab',    'Slab - Waffle Pod or Waffle Slab',        17),
('SlabOnGround',                 'Slab on ground',                          18),
('SteelColumns',                 'Steel columns',                           19),
('SteelFrame',                   'Steel Frame',                             20),
('SteelFootings',                'Steel Footings',                          21),
('Stumps',                       'Stumps',                                  22),
('SuspendedSteelFrameWithCFC',   'Suspended Steel Frame with CFC Sheets',   23),
('SuspendedTimberFrame',         'Suspended Timber Frame',                  24),
('TimberStumps',                 'Timber Stumps',                           25),
('TimberWithConcreteAreas',      'Timber with Concrete Areas',              26),
('TimberWithHardboard',          'Timber with hardboard',                   27),
('Other',                        'Other',                                   28),
('NotApplicable',                'Not Applicable',                          29);


-- Other Building Elements

INSERT INTO public."O_OtherBuildingElements"
("Code",                     "Name",                                     "SortOrder")
VALUES
('FencePerforatedMaterials', 'Fence - Perforated Materials / Wire Mesh', 1),
('FencePostAndRail',         'Fence - Post and Rail Construction',       2),
('FenceStone',               'Fence - Stone',                            3),
('Footpath',                 'Footpath',                                 4),
('Garage',                   'Garage',                                   5),
('PartyWalls',               'Party Walls',                              6),
('Pergola',                  'Pergola',                                  7),
('Pool',                     'Pool',                                     8),
('Porch',                    'Porch',                                    9),
('RetainingWalls',           'Retaining Walls',                          10),
('Shed',                     'Shed',                                     11),
('WaterTank',                'Water Tank',                               12),
('Other',                    'Other',                                    13),
('NotApplicable',            'Not Applicable',                           14);


-- Other Timber Building Elements

INSERT INTO public."O_OtherTimberBldgElements"
("Code",                              "Name",                                 "SortOrder")
VALUES
('Eaves',                             'Eaves',                                1),
('ExternalJoinery',                   'External Joinery',                     2),
('Fascias',                           'Fascias',                              3),
('FloatingFloor',                     'Floating Floor',                       4),
('Floorboards',                       'Floorboards',                          5),
('InternalJoinery',                   'Internal Joinery',                     6),
('LandscapingTimbersAndConstruction', 'Landscaping Timbers and Construction', 7),
('ParquetryFlooring',                 'Parquetry Flooring',                   8),
('Patio',                             'Patio',                                9),
('PorchPatio',                        'Porch / Patio',                        10),
('SkirtingBoards',                    'Skirting Boards',                      11),
('StairRailing',                      'Stair Railing',                        12),
('Staircase',                         'Staircase',                            13),
('Stumps',                            'Stumps',                               14),
('TimberWallPanneling',               'Timber Wall Panneling',                15),
('VerandaPosts',                      'Veranda Posts',                        16),
('Weatherboards',                     'Weatherboards',                        17),
('WindowFrames',                      'Window Frames',                        18),
('Other',                             'Other',                                19),
('NotApplicable',                     'Not Applicable',                       20);


-- Roof

INSERT INTO public."O_Roof"
("Code",           "Name",                              "SortOrder")
VALUES
('Aluminium',      'Aluminium',                         1),
('CoatedMetal',    'Coated Metal',                      2),
('CorrugatedIron', 'Corrugated Iron (e.g. Colourbond)', 3),
('Flat',           'Flat',                              4),
('Iron',           'Iron',                              5),
('Pitched',        'Pitched',                           6),
('Slate',          'Slate',                             7),
('SteelFramed',    'Steel Framed',                      8),
('Tiled',          'Tiled',                             9),
('Tiles',          'Tiles',                             10),
('TimberFramed',   'Timber Framed',                     11),
('Other',          'Other',                             12),
('NotApplicable',  'Not Applicable',                    13);


-- Walls

INSERT INTO public."O_Walls"
("Code",                    "Name",                                        "SortOrder")
VALUES
('Aluminium',               'Aluminium',                                   1),
('BrickVeneer',             'Brick Veneer',                                2),
('BrickVeneerSteelFramed',  'Brick Veneer (Steel Framed)',                 3),
('BrickVeneerTimberFramed', 'Brick Veneer (Timber Framed)',                4),
('CavityBrick',             'Cavity Brick',                                5),
('CoatedMetalSheeting',     'Coated Metal Sheeting',                       6),
('Colourbond',              'Colourbond',                                  7),
('ConcreteBlock',           'Concrete Block',                              8),
('ConcretePanel',           'Concrete Panel',                              9),
('EIFS',                    'EIFS (External Insulation Finishing System)', 10),
('FibreCementSheets',       'Fibre Cement Sheets',                         11),
('FullBrick',               'Full Brick',                                  12),
('HardPlank',               'Hard-plank',                                  13),
('HebelClad',               'Hebel Clad',                                  14),
('LightWeightWallClad',     'Light Weight Wall Clad',                      15),
('Plywood',                 'Plywood',                                     16),
('ProfiledMetal',           'Profiled metal',                              17),
('Rendered',                'Rendered',                                    18),
('SolidMasonry',            'Solid Masonry',                               19),
('SteelFrameClad',          'Steel Frame Clad',                            20),
('Stone',                   'Stone',                                       21),
('StructuralConcrete',      'Structural Concrete',                         22),
('StructuralMasonry',       'Structural Masonry',                          23),
('Stucco',                  'Stucco',                                      24),
('TimberFramedAndClad',     'Timber Framed and Clad',                      25),
('Weatherboards',           'Weatherboards',                               26),
('Other',                   'Other',                                       27),
('NotApplicable',           'Not Applicable',                              28);


-- Weather

INSERT INTO public."O_Weather"
("Code",     "Name",     "SortOrder")
VALUES
('Fine',     'Fine',     1),
('Overcast', 'Overcast', 2),
('Raining',  'Raining',  3),
('Storms',   'Storms',   4);


-- Location

INSERT INTO public."O_Location"
("Code",     "Name",     "SortOrder")
VALUES
('Fine',     'Fine',     1),
('Overcast', 'Overcast', 2),
('Raining',  'Raining',  3),
('Storms',   'Storms',   4);


-- GlobalSettings

INSERT INTO public."GlobalSettings"
("Code",                  "Description",                                 "Value")
VALUES
('ForceRefreshAuthToken', 'Force refresh of auth token on next request', '0');